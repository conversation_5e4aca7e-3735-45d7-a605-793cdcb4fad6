<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

// Props interface
interface Props {
  useBot: any;
}

const props = defineProps<{
  useBot: any;
}>();

// Sử dụng botHook từ props thay vì individual props/emits
const { filterVisible, handleFilter, drawerValues } = props.useBot;

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Bot Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by status"),
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Draft"), value: "draft" },
      { label: $t("Pause"), value: "pause" }
    ],
    colProps: { span: 12 }
  }
];
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :model-visible="filterVisible"
    :model-value="drawerValues"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="filterVisible = false">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          @click="handleFilter"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
